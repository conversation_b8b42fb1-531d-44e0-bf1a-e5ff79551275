import sys
import asyncio
import os
import time
import pytest
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState, InterruptState
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, InterruptDetectionConfig, get_interrupt_config
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from schemas.layer2_schema import TTSInputSchema, TTSOutputSchema

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)
        
        # test 1 - test getting a workflow
        workflow = await sm.getWorkflow()
        print("Workflow:", workflow)

        # test 2 - test getting all pipelines
        pipelines = await sm.getFullPipelineMap()
        print("Pipelines:", pipelines)

        # test 3 - test getting the current workflow state
        current_state = await sm.getCurrentWorkflowState()
        print("Current State:", current_state)

        # test 4 - test getting the current pipeline state
        current_pipeline_state = await sm.getCurrentPipelineState()
        print("Current Pipeline State:", current_pipeline_state)

        # test 5 - test executing a pipeline
        pipeline = await sm.getCurrentPipeline()
        print("Current Pipeline:", pipeline)

        # test 6 - test getting prohibited actions
        prohibited_actions = await sm.getProhibitedActions()
        print("Prohibited Actions:", prohibited_actions)

        # test 7 - test getting allowed actions
        allowed_actions = await sm.getAllowedActions()
        print("Allowed Actions:", allowed_actions)

        # test 8 - execution 

        # Step 1: Simulate greeting state
        
        greeting_tts_input = {"text": "Hello, how can I assist you today?"}
        greeting_tts_result = await sm.executePipelineState(greeting_tts_input)
        print("\ngreeting_tts_result:", greeting_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("Iniquiry")
        Iniquiry_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        Iniquiry_stt_result = await sm.executePipelineState(Iniquiry_stt_input)
        print("\nIniquiry_stt_result:", Iniquiry_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        Iniquiry_preprocessing_input = {}
        Iniquiry_preprocessing_result = await sm.executePipelineState(Iniquiry_preprocessing_input)
        print("\nIniquiry_preprocessing_result:", Iniquiry_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        Iniquiry_filler_tts_input = {}
        Iniquiry_filler_tts_result = await sm.executePipelineState(Iniquiry_filler_tts_input)
        print("\nIniquiry_filler_tts_result:", Iniquiry_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        Iniquiry_processing_input = {}
        Iniquiry_processing_result = await sm.executePipelineState(Iniquiry_processing_input)
        print("\nIniquiry_processing_result:", Iniquiry_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        Iniquiry_tts_input = {}
        Iniquiry_tts_result = await sm.executePipelineState(Iniquiry_tts_input)
        print("\nIniquiry_tts_result:", Iniquiry_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("CheckBalance")
        check_balance_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        check_balance_stt_result = await sm.executePipelineState(check_balance_stt_input)
        print("\ncheck_balance_stt_result:", check_balance_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        check_balance_preprocessing_input = {}
        check_balance_preprocessing_result = await sm.executePipelineState(check_balance_preprocessing_input)
        print("\ncheck_balance_preprocessing_result:", check_balance_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        check_balance_filler_tts_input = {}
        check_balance_filler_tts_result = await sm.executePipelineState(check_balance_filler_tts_input)
        print("\ncheck_balance_filler_tts_result:", check_balance_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        check_balance_processing_input = {}
        check_balance_processing_result = await sm.executePipelineState(check_balance_processing_input)
        print("\ncheck_balance_processing_result:", check_balance_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        check_balance_tts_input = {}
        check_balance_tts_result = await sm.executePipelineState(check_balance_tts_input)
        print("\ncheck_balance_tts_result:", check_balance_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # go to goodbye state
        await sm.transitionWorkflow("Goodbye")
        goodbye_tts_input = {"text": "Thank you for using our service. Goodbye!"}
        goodbye_tts_result = await sm.executePipelineState(goodbye_tts_input)
        print("\ngoodbye_tts_result:", goodbye_tts_result)

    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )


# ============================================================================
# COMPREHENSIVE INTERRUPT SYSTEM TESTS
# ============================================================================

class TestInterruptSystemIntegration:
    """
    Comprehensive test suite for interrupt handling system integration.

    Tests the complete end-to-end interrupt workflow including:
    - VAD-based interrupt detection during TTS playback
    - TTS pause/resume functionality with partial playback tracking
    - State transition management via StateManager
    - Differentiation between reversible and irreversible actions
    - Proper queuing of user input for post-TTS completion (irreversible actions)
    - Immediate processing of new user input (reversible actions)
    """

    @pytest.fixture
    async def setup_interrupt_test_environment(self):
        """Setup test environment with StateManager and interrupt configuration."""
        session_id = "test_interrupt_session"
        user_id = "test_interrupt_user"

        # Setup logging
        setup_development_logging()

        # Create StateManager
        state_manager = await StateManager.create("banking_workflow.json", session_id, user_id)

        # Create interrupt configuration
        interrupt_config = InterruptConfig(
            detection=InterruptDetectionConfig(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )

        yield {
            "state_manager": state_manager,
            "session_id": session_id,
            "user_id": user_id,
            "interrupt_config": interrupt_config
        }

        # Cleanup
        cleanup_logger()

    @pytest.mark.asyncio
    async def test_tts_state_with_interrupt_monitoring_setup(self, setup_interrupt_test_environment):
        """Test TTS state execution with interrupt monitoring setup."""
        env = await setup_interrupt_test_environment
        state_manager = env["state_manager"]

        # Create TTSState
        tts_state = TTSState(
            state_id="test_tts",
            agent_registry=state_manager.agent_registry,
            session_id=env["session_id"]
        )

        # Mock TTS agent response
        mock_tts_agent = AsyncMock()
        mock_tts_agent.process.return_value = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={
                "audio_path": "/test/tts_output.mp3",
                "latencyTTS": 1.2,
                "duration": 5.0
            },
            meta={"tts_generation": "success"}
        )

        # Mock agent registry
        state_manager.agent_registry.getAgent = Mock(return_value=mock_tts_agent)

        # Test TTS processing
        tts_input = {"text": "Hello, this is a test message for interrupt handling."}
        result = await tts_state.process(tts_input)

        assert result.status == StatusType.SUCCESS
        assert result.outputs["audio_path"] == "/test/tts_output.mp3"
        assert "latencyTTS" in result.outputs

        # Verify TTS agent was called
        mock_tts_agent.process.assert_called_once()

    @pytest.mark.asyncio
    async def test_interrupt_state_creation_and_configuration(self, setup_interrupt_test_environment):
        """Test InterruptState creation with proper configuration."""
        env = await setup_interrupt_test_environment

        # Create InterruptState with configuration
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )

        # Verify configuration
        assert interrupt_state is not None
        assert interrupt_state.vad_threshold == 0.01
        assert interrupt_state.confirmation_window == 0.5
        assert interrupt_state.min_interrupt_duration == 0.3

        # Verify interrupt messages are configured
        assert hasattr(interrupt_state, 'interrupt_messages')
        assert "reversible" in interrupt_state.interrupt_messages
        assert "irreversible" in interrupt_state.interrupt_messages
        assert "unknown" in interrupt_state.interrupt_messages

