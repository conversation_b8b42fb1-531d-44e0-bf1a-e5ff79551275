import sys
import asyncio
import os
import time
import pytest
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState, InterruptState
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, GlobalInterruptSettings, get_interrupt_config
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from schemas.layer2_schema import TTSInputSchema, TTSOutputSchema

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)
        
        # test 1 - test getting a workflow
        workflow = await sm.getWorkflow()
        print("Workflow:", workflow)

        # test 2 - test getting all pipelines
        pipelines = await sm.getFullPipelineMap()
        print("Pipelines:", pipelines)

        # test 3 - test getting the current workflow state
        current_state = await sm.getCurrentWorkflowState()
        print("Current State:", current_state)

        # test 4 - test getting the current pipeline state
        current_pipeline_state = await sm.getCurrentPipelineState()
        print("Current Pipeline State:", current_pipeline_state)

        # test 5 - test executing a pipeline
        pipeline = await sm.getCurrentPipeline()
        print("Current Pipeline:", pipeline)

        # test 6 - test getting prohibited actions
        prohibited_actions = await sm.getProhibitedActions()
        print("Prohibited Actions:", prohibited_actions)

        # test 7 - test getting allowed actions
        allowed_actions = await sm.getAllowedActions()
        print("Allowed Actions:", allowed_actions)

        # test 8 - execution 

        # Step 1: Simulate greeting state
        
        greeting_tts_input = {"text": "Hello, how can I assist you today?"}
        greeting_tts_result = await sm.executePipelineState(greeting_tts_input)
        print("\ngreeting_tts_result:", greeting_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("Iniquiry")
        Iniquiry_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        Iniquiry_stt_result = await sm.executePipelineState(Iniquiry_stt_input)
        print("\nIniquiry_stt_result:", Iniquiry_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        Iniquiry_preprocessing_input = {}
        Iniquiry_preprocessing_result = await sm.executePipelineState(Iniquiry_preprocessing_input)
        print("\nIniquiry_preprocessing_result:", Iniquiry_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        Iniquiry_filler_tts_input = {}
        Iniquiry_filler_tts_result = await sm.executePipelineState(Iniquiry_filler_tts_input)
        print("\nIniquiry_filler_tts_result:", Iniquiry_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        Iniquiry_processing_input = {}
        Iniquiry_processing_result = await sm.executePipelineState(Iniquiry_processing_input)
        print("\nIniquiry_processing_result:", Iniquiry_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        Iniquiry_tts_input = {}
        Iniquiry_tts_result = await sm.executePipelineState(Iniquiry_tts_input)
        print("\nIniquiry_tts_result:", Iniquiry_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("CheckBalance")
        check_balance_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        check_balance_stt_result = await sm.executePipelineState(check_balance_stt_input)
        print("\ncheck_balance_stt_result:", check_balance_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        check_balance_preprocessing_input = {}
        check_balance_preprocessing_result = await sm.executePipelineState(check_balance_preprocessing_input)
        print("\ncheck_balance_preprocessing_result:", check_balance_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        check_balance_filler_tts_input = {}
        check_balance_filler_tts_result = await sm.executePipelineState(check_balance_filler_tts_input)
        print("\ncheck_balance_filler_tts_result:", check_balance_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        check_balance_processing_input = {}
        check_balance_processing_result = await sm.executePipelineState(check_balance_processing_input)
        print("\ncheck_balance_processing_result:", check_balance_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        check_balance_tts_input = {}
        check_balance_tts_result = await sm.executePipelineState(check_balance_tts_input)
        print("\ncheck_balance_tts_result:", check_balance_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # go to goodbye state
        await sm.transitionWorkflow("Goodbye")
        goodbye_tts_input = {"text": "Thank you for using our service. Goodbye!"}
        goodbye_tts_result = await sm.executePipelineState(goodbye_tts_input)
        print("\ngoodbye_tts_result:", goodbye_tts_result)

    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )
    
# ============================================================================
# COMPREHENSIVE INTERRUPT SYSTEM TESTS
# ============================================================================

class TestInterruptSystem:
    """
    Comprehensive test suite for voice agent interrupt system.

    Tests the complete interrupt workflow including:
    - VAD detection during TTS playback
    - TTS pause/resume functionality with partial playback tracking
    - StateManager coordination of interrupt handling
    - Differentiation between reversible vs irreversible actions
    - Proper queuing of user input for reversible actions vs immediate processing
    - End-to-end validation that StateManager executes queued user input
    """

    @pytest.fixture
    async def setup_interrupt_test_environment(self):
        """Setup test environment with StateManager and interrupt configuration."""
        session_id = "test_interrupt_session"
        user_id = "test_interrupt_user"

        # Setup logging
        setup_development_logging()

        # Create StateManager
        state_manager = await StateManager.create("banking_workflow.json", session_id, user_id)

        # Create interrupt configuration
        interrupt_config = InterruptConfig(
            global_settings=GlobalInterruptSettings(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )

        yield {
            "state_manager": state_manager,
            "session_id": session_id,
            "user_id": user_id,
            "interrupt_config": interrupt_config
        }

        # Cleanup
        cleanup_logger()

    @pytest.mark.asyncio
    async def test_vad_detection_during_tts_playback(self, setup_interrupt_test_environment):
        """Test VAD detection triggering interrupts during TTS playback."""
        test_env = await setup_interrupt_test_environment.__anext__()
        state_manager = test_env["state_manager"]
        session_id = test_env["session_id"]
        interrupt_config = test_env["interrupt_config"]

        # Create InterruptState
        interrupt_state = InterruptState(
            state_id="test_interrupt_vad",
            agent_registry=state_manager.agent_registry,
            session_id=session_id,
            interrupt_config=interrupt_config
        )

        # Test 1: No voice activity detected
        input_data_no_voice = {
            "audio_data": None,
            "current_tts_audio_path": "/test/audio.mp3",
            "playback_position": 1.5
        }

        result = await interrupt_state.process(input_data_no_voice)

        assert result.status == StatusType.SUCCESS
        assert result.outputs["interrupt_detected"] == False
        assert result.outputs["voice_activity"] == False
        assert result.meta["interrupt_state"] == "no_voice"

        # Test 2: Voice activity detected and confirmed
        mock_audio_data = b"mock_audio_data_with_voice"
        input_data_with_voice = {
            "audio_data": mock_audio_data,
            "current_tts_audio_path": "/test/audio.mp3",
            "playback_position": 2.0,
            "action_context": {"reversible": True}
        }

        # Mock VAD detection to return voice activity
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=True):
                result = await interrupt_state.process(input_data_with_voice)

                assert result.status == StatusType.SUCCESS
                assert result.outputs["interrupt_detected"] == True
                assert result.outputs["interrupt_confirmed"] == True
                assert result.outputs["voice_activity"] == True
                assert "acknowledgment_message" in result.outputs

    @pytest.mark.asyncio
    async def test_tts_pause_resume_with_partial_playback_tracking(self, setup_interrupt_test_environment):
        """Test TTS pause/resume functionality with partial playback tracking."""
        test_env = await setup_interrupt_test_environment.__anext__()
        state_manager = test_env["state_manager"]
        session_id = test_env["session_id"]

        # Step 1: Start TTS playback
        tts_input = {"text": "This is a test message for interrupt handling during TTS playback."}

        # Mock TTS agent to return audio path
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/tts_audio.mp3", "latencyTTS": 0.5},
            meta={}
        )

        # Mock the TTS state execution
        with patch.object(state_manager, 'executePipelineState', return_value=mock_tts_result):
            # Transition to TTS state
            await state_manager.transitionWorkflow("Greeting")
            await state_manager.transitionPipeline("tts")

            tts_result = await state_manager.executePipelineState(tts_input)

            assert tts_result.status == StatusType.SUCCESS
            assert "audio_path" in tts_result.outputs

        # Step 2: Simulate interrupt during playback
        interrupt_input = {
            "audio_data": b"user_voice_interrupt",
            "current_tts_audio_path": tts_result.outputs["audio_path"],
            "playback_position": 3.2  # Interrupted at 3.2 seconds
        }

        # Mock interrupt detection and handling
        with patch.object(state_manager, '_start_tts_interrupt_monitoring'):
            with patch.object(state_manager.memory_manager, 'set_tts_playback_state') as mock_set_playback:
                with patch.object(state_manager.memory_manager, 'get_tts_playback_state') as mock_get_playback:
                    mock_get_playback.return_value = {
                        "audio_path": tts_result.outputs["audio_path"],
                        "status": "interrupted",
                        "playback_position": 3.2,
                        "message_hash": "test_hash"
                    }

                    # Test playback state tracking
                    playback_state = await state_manager.memory_manager.get_tts_playback_state()

                    assert playback_state["status"] == "interrupted"
                    assert playback_state["playback_position"] == 3.2
                    assert playback_state["audio_path"] == tts_result.outputs["audio_path"]

