{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Iniquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"reversible": true, "side_effects": false, "confirmation_required": false, "interrupt_message": "Allow me to finish this greeting, then I'll respond to what you said.", "description": "Greeting messages can be safely interrupted as they have no side effects"}}, "Iniquiry": {"id": "Iniquiry", "type": "inform", "layer2_id": "l2_iniquiry_banking_system_v2", "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"reversible": true, "side_effects": false, "confirmation_required": false, "interrupt_message": "Allow me to finish processing your request, then I'll respond to what you said.", "description": "User inquiry processing can be safely interrupted and restarted"}}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "expected_input": ["account_id"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "interrupt_config": {"reversible": true, "side_effects": false, "confirmation_required": false, "interrupt_message": "Allow me to finish checking your balance, then I'll respond to what you said.", "description": "Balance inquiries are read-only operations that can be safely interrupted"}}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"reversible": false, "side_effects": true, "confirmation_required": true, "interrupt_message": "The transfer has already been completed. If something went wrong, let me know and I'll help fix it.", "description": "Fund transfers are irreversible financial transactions with permanent side effects"}}, "exchangeRate": {"id": "exchangeRate", "type": "transaction", "layer2_id": "l2_exchange_rate_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"reversible": true, "side_effects": false, "confirmation_required": false, "interrupt_message": "Allow me to finish getting the current exchange rates, then I'll respond to what you said.", "description": "Exchange rate inquiries are informational and can be safely interrupted"}}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["TTS"], "interrupt_config": {"reversible": true, "side_effects": false, "confirmation_required": false, "interrupt_message": "Allow me to finish this goodbye message, then I'll respond to what you said.", "description": "Goodbye messages can be safely interrupted as they are just polite closures"}}}}}