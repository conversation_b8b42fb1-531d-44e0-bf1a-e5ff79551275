"""
Interrupt Coordinator - Central coordination for interrupt handling in voice agent system.

This module provides the main coordination logic for handling interrupts during workflow execution,
integrating with StateManager, InterruptState, and ActionReversibilityDetector.
"""

import asyncio
from typing import Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from core.logging.logger_config import get_module_logger
from core.state_manager.state_output import InterruptState
from core.interruption.action_reversibility import ActionReversibilityDetector
from schemas.outputSchema import StateOutput, StatusType, StatusCode

logger = get_module_logger("interrupt_coordinator")


class ExecutionState(Enum):
    """Current execution state of the workflow."""
    RUNNING = "running"
    PAUSED = "paused"
    INTERRUPTED = "interrupted"
    RESUMING = "resuming"


@dataclass
class ExecutionContext:
    """Context information for paused/interrupted execution."""
    state_id: str
    pipeline_step: str
    input_data: Dict[str, Any]
    execution_function: Callable
    pause_timestamp: str
    reversible: bool
    tts_audio_path: Optional[str] = None
    tts_playback_position: Optional[float] = None


class InterruptCoordinator:
    """
    Central coordinator for interrupt handling in voice agent workflows.
    
    Responsibilities:
    - Coordinate between StateManager and interrupt detection
    - Manage execution pause/resume logic
    - Handle reversible vs irreversible action flows
    - Integrate with existing InterruptState and ActionReversibilityDetector
    """

    def __init__(self, state_manager, memory_manager, interrupt_config):
        self.state_manager = state_manager
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config
        self.logger = logger
        
        # Execution state management
        self.execution_state = ExecutionState.RUNNING
        self.paused_context: Optional[ExecutionContext] = None
        self.interrupt_in_progress = False
        
        # Components
        self.interrupt_state = None
        self.reversibility_detector = ActionReversibilityDetector()
        
        # Real-time monitoring
        self.audio_monitoring_active = False
        self.current_tts_playback = None

    async def initialize(self):
        """Initialize the interrupt coordinator."""
        try:
            # Create InterruptState instance
            self.interrupt_state = InterruptState(
                state_id="interrupt_coordinator",
                agent_registry=self.state_manager.agent_registry,
                session_id=self.state_manager.session_id,
                interrupt_config=self.interrupt_config
            )
            
            self.logger.info(
                "InterruptCoordinator initialized",
                action="initialize",
                layer="interrupt_coordination"
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to initialize InterruptCoordinator",
                action="initialize",
                reason=str(e),
                layer="interrupt_coordination"
            )
            raise

    async def execute_with_interrupt_support(self, execution_function: Callable, 
                                           input_data: Dict[str, Any],
                                           state_id: str, pipeline_step: str) -> StateOutput:
        """
        Execute a function with interrupt support.
        
        This is the main entry point for executing workflow steps with interrupt handling.
        """
        try:
            self.logger.info(
                "Starting execution with interrupt support",
                action="execute_with_interrupt_support",
                input_data={"state_id": state_id, "pipeline_step": pipeline_step},
                layer="interrupt_coordination"
            )
            
            # Check if we're resuming from an interrupt
            if self.execution_state == ExecutionState.PAUSED and self.paused_context:
                return await self._resume_execution()
            
            # Get current state configuration for reversibility
            current_state_config = self.state_manager.get_state(state_id)
            reversible = self._is_action_reversible(current_state_config)
            
            # Create execution context
            execution_context = ExecutionContext(
                state_id=state_id,
                pipeline_step=pipeline_step,
                input_data=input_data,
                execution_function=execution_function,
                pause_timestamp=datetime.now().isoformat(),
                reversible=reversible
            )
            
            # Start interrupt monitoring if this is a TTS step
            if pipeline_step == "tts":
                await self._start_tts_interrupt_monitoring(execution_context)
            
            # Execute the function with interrupt monitoring
            result = await self._execute_with_monitoring(execution_function, input_data, execution_context)
            
            # Stop interrupt monitoring
            await self._stop_interrupt_monitoring()
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Error in execute_with_interrupt_support",
                action="execute_with_interrupt_support",
                reason=str(e),
                layer="interrupt_coordination"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Execution error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def handle_interrupt(self, audio_data: bytes, 
                             current_tts_audio_path: Optional[str] = None,
                             playback_position: Optional[float] = None,
                             user_input: Optional[str] = None) -> StateOutput:
        """
        Handle an interrupt event.
        
        This is called when an interrupt is detected during execution.
        """
        try:
            if self.interrupt_in_progress:
                self.logger.warning(
                    "Interrupt already in progress, ignoring new interrupt",
                    action="handle_interrupt",
                    layer="interrupt_coordination"
                )
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="Interrupt already in progress",
                    code=StatusCode.OK,
                    outputs={"interrupt_handled": False},
                    meta={"reason": "interrupt_already_in_progress"}
                )
            
            self.interrupt_in_progress = True
            
            self.logger.info(
                "Handling interrupt",
                action="handle_interrupt",
                input_data={
                    "has_audio_data": audio_data is not None,
                    "tts_audio_path": current_tts_audio_path,
                    "playback_position": playback_position
                },
                layer="interrupt_coordination"
            )
            
            # Pause current execution if running
            if self.execution_state == ExecutionState.RUNNING:
                await self._pause_current_execution(playback_position)
            
            # Process interrupt using InterruptState
            interrupt_input = {
                "audio_data": audio_data,
                "current_tts_audio_path": current_tts_audio_path or self.current_tts_playback,
                "playback_position": playback_position or 0.0,
                "user_input": user_input or "[Interrupt detected]"
            }
            
            # Get workflow state configuration for context
            context = await self._build_interrupt_context()
            
            # Process interrupt
            interrupt_result = await self.interrupt_state.process(interrupt_input, context)
            
            # Handle the interrupt result
            await self._handle_interrupt_result(interrupt_result)
            
            self.interrupt_in_progress = False
            
            return interrupt_result
            
        except Exception as e:
            self.interrupt_in_progress = False
            self.logger.error(
                "Error handling interrupt",
                action="handle_interrupt",
                reason=str(e),
                layer="interrupt_coordination"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt handling error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def _execute_with_monitoring(self, execution_function: Callable, 
                                     input_data: Dict[str, Any],
                                     execution_context: ExecutionContext) -> StateOutput:
        """Execute function while monitoring for interrupts."""
        try:
            self.execution_state = ExecutionState.RUNNING
            
            # Execute the function
            result = await execution_function(input_data)
            
            # If TTS, store playback information
            if execution_context.pipeline_step == "tts" and result.status == StatusType.SUCCESS:
                self.current_tts_playback = result.outputs.get("audio_path")
                execution_context.tts_audio_path = self.current_tts_playback
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Error in monitored execution",
                action="_execute_with_monitoring",
                reason=str(e),
                layer="interrupt_coordination"
            )
            raise

    def _is_action_reversible(self, state_config) -> bool:
        """Determine if the current action is reversible."""
        if state_config and hasattr(state_config, 'interrupt_config') and state_config.interrupt_config:
            return state_config.interrupt_config.reversible
        return True  # Default to reversible for safety

    async def _build_interrupt_context(self) -> Dict[str, Any]:
        """Build context for interrupt processing."""
        current_state_config = None
        if self.paused_context:
            current_state_config = self.state_manager.get_state(self.paused_context.state_id)
        
        context = {
            "session_id": self.state_manager.session_id,
            "memory_manager": self.memory_manager,
            "workflow_state_config": {
                "interrupt_config": current_state_config.interrupt_config.model_dump() if current_state_config and current_state_config.interrupt_config else None,
                "reversible": current_state_config.interrupt_config.reversible if current_state_config and current_state_config.interrupt_config else True
            }
        }
        
        return context

    async def _pause_current_execution(self, playback_position: Optional[float] = None):
        """Pause the current execution."""
        self.execution_state = ExecutionState.PAUSED
        if self.paused_context and playback_position is not None:
            self.paused_context.tts_playback_position = playback_position
        
        self.logger.info(
            "Execution paused for interrupt handling",
            action="_pause_current_execution",
            layer="interrupt_coordination"
        )

    async def _start_tts_interrupt_monitoring(self, execution_context: ExecutionContext):
        """Start monitoring for interrupts during TTS playback."""
        self.audio_monitoring_active = True
        # In a real implementation, this would start background audio monitoring
        self.logger.info(
            "Started TTS interrupt monitoring",
            action="_start_tts_interrupt_monitoring",
            layer="interrupt_coordination"
        )

    async def _stop_interrupt_monitoring(self):
        """Stop interrupt monitoring."""
        self.audio_monitoring_active = False
        self.logger.info(
            "Stopped interrupt monitoring",
            action="_stop_interrupt_monitoring",
            layer="interrupt_coordination"
        )

    async def _handle_interrupt_result(self, interrupt_result: StateOutput):
        """Handle the result of interrupt processing."""
        if interrupt_result.status == StatusType.SUCCESS:
            action_reversible = interrupt_result.outputs.get("action_reversible")
            should_queue = interrupt_result.outputs.get("should_queue_input", False)
            
            if action_reversible == "reversible" and should_queue:
                # For reversible actions, we can resume after acknowledgment
                self.logger.info(
                    "Reversible action interrupted - will resume after acknowledgment",
                    action="_handle_interrupt_result",
                    layer="interrupt_coordination"
                )
            else:
                # For irreversible actions, clear paused context
                self.paused_context = None
                self.execution_state = ExecutionState.RUNNING
                self.logger.info(
                    "Irreversible action interrupted - cleared execution context",
                    action="_handle_interrupt_result",
                    layer="interrupt_coordination"
                )

    async def _resume_execution(self) -> StateOutput:
        """Resume paused execution."""
        if not self.paused_context:
            raise RuntimeError("No paused execution context to resume")
        
        self.logger.info(
            "Resuming paused execution",
            action="_resume_execution",
            input_data={"state_id": self.paused_context.state_id},
            layer="interrupt_coordination"
        )
        
        self.execution_state = ExecutionState.RESUMING
        
        try:
            # Resume execution from where it left off
            result = await self.paused_context.execution_function(self.paused_context.input_data)
            
            # Clear paused context
            self.paused_context = None
            self.execution_state = ExecutionState.RUNNING
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Error resuming execution",
                action="_resume_execution",
                reason=str(e),
                layer="interrupt_coordination"
            )
            raise
